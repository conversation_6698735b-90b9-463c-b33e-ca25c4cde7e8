package com.pugwoo.admin.utils;

import com.pugwoo.admin.utils.aidto.ChatCompletionReq;
import com.pugwoo.admin.utils.aidto.ChatCompletionResp;
import com.pugwoo.admin.utils.aidto.ChatCompletionStreamResp;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.net.Browser;
import com.pugwoo.wooutils.net.HttpResponse;
import com.pugwoo.wooutils.string.StringTools;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.SneakyThrows;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;

/**
 * 封装了调用大模型api的工具。
 * 目前仅提供非流式的结果，因为对于工具类来说，没有必要使用流式。
 * <p>
 * 但是特别的是，对于有些模型，它的输出特别慢，例如deepseek-r1，等它思考的过程非常慢，如果不使用流式，非常容易被断，因此这个工具类还是支持将流式转成非流式。
 * <p>
 * ref <a href="https://code.pugwoo.com/learning/openai-sdk-demo">...</a>
 */
@Getter @Setter
@NoArgsConstructor
public class AIUtils {

    /**填写到根url即可，不需要含/v1 */
    private String apiUrl = "https://api.openai.com";
    private String apiKey;

    /**读超时秒数，默认10分钟 */
    private int readTimeoutSecond = 600;

    public AIUtils(String apiUrl, String apiKey) {
        this.apiUrl = apiUrl;
        this.apiKey = apiKey;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Message {
        private String role;
        private String content;
        private List<String> imagesBase64;
    }

    /**
     * 最简单的单个问题，没有上下文，直接返回字符串的结果
     */
    @SneakyThrows
    public String chat(String model, String question) {
        return chat(model, List.of(new Message("user", question)));
    }

    /**
     * @param message 支持传递系统systemPrompt或历史对话消息
     */
    @SneakyThrows
    public String chat(String model, List<Message> message) {
        Browser browser = createBrowser();
        String url = getApiUrl();
        ChatCompletionReq req = createChatRequest(model, message, false);

        HttpResponse httpResponse = browser.postJson(url, req);
        validateResponse(httpResponse);

        String contentString = httpResponse.getContentString();
        ChatCompletionResp resp = JSON.parse(contentString, ChatCompletionResp.class);
        if (resp == null || resp.getChoices() == null || resp.getChoices().isEmpty()
            || resp.getChoices().getFirst().getMessage() == null) {
            throw new IOException("call api fail, code:" + httpResponse.getResponseCode() + ", msg:" + contentString);
        }
        return resp.getChoices().getFirst().getMessage().getContent();
    }

    /**
     * 对于有些模型，它的输出特别慢，例如deepseek-r1，等它思考的过程非常慢，如果不使用流式，非常容易被断，因此这个工具类还是支持将流式转成非流式
     */
    @SneakyThrows
    public String chatFromStream(String model, String question) {
        return chatFromStream(model, List.of(new Message("user", question)));
    }

    @SneakyThrows
    public String chatFromStream(String model, List<Message> message) {
        Browser browser = createBrowser();
        String url = getApiUrl();
        ChatCompletionReq req = createChatRequest(model, message, true);

        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        HttpResponse httpResponse = browser.postJson(url, req, byteArrayOutputStream);
        validateResponse(httpResponse);

        return processStreamResponse(byteArrayOutputStream.toString(), httpResponse);
    }

    private Browser createBrowser() {
        Browser browser = new Browser();
        browser.setReadTimeoutSeconds(readTimeoutSecond);
        browser.addRequestHeader("Authorization", "Bearer " + apiKey);
        return browser;
    }

    private String getApiUrl() {
        return apiUrl + "/v1/chat/completions";
    }

    private ChatCompletionReq createChatRequest(String model, List<Message> messages, boolean stream) {
        ChatCompletionReq req = new ChatCompletionReq();
        req.setModel(model);
        req.setStream(stream);
        for (Message msg : messages) {
            req.getMessages().add(new ChatCompletionReq.Message(msg.getRole(), msg.getContent()));
        }
        return req;
    }

    private void validateResponse(HttpResponse httpResponse) throws IOException {
        if (httpResponse.getResponseCode() != 200) {
            throw new IOException("call api fail, code:" + httpResponse.getResponseCode() + 
                ", msg:" + httpResponse.getContentString());
        }
    }

    private String processStreamResponse(String output, HttpResponse httpResponse) throws IOException {
        String[] lines = StringTools.splitLines(output);
        StringBuilder sb = new StringBuilder();
        for (String line : lines) {
            if (line.startsWith("data: ")) {
                line = line.substring("data: ".length());
                if ("[DONE]".equals(line)) {
                    return sb.toString();
                }
                processStreamLine(line, sb, output, httpResponse);
            }
        }
        return sb.toString();
    }

    private void processStreamLine(String line, StringBuilder sb, String output, HttpResponse httpResponse) throws IOException {
        try {
            ChatCompletionStreamResp resp = JSON.parse(line, ChatCompletionStreamResp.class);
            if (resp != null && resp.getChoices() != null) {
                for (ChatCompletionStreamResp.Choice choice : resp.getChoices()) {
                    ChatCompletionStreamResp.Delta delta = choice.getDelta();
                    if (delta != null && StringTools.isNotEmpty(delta.getContent())) {
                        sb.append(delta.getContent());
                    }
                }
            }
        } catch (Exception e) {
            throw new IOException("call api fail, code:" + httpResponse.getResponseCode() + ", msg:" + output, e);
        }
    }

}
